#!/usr/bin/env python3
"""
Upload Manga to TrueNAS Server via SFTP
Syncs the local manga/ folder to the remote server, uploading only new files.
Matches existing folder names on the server.
"""

import os
import re
import sys
from pathlib import Path
from typing import Set, List, Optional

try:
    import paramiko
except ImportError:
    print("Error: paramiko library not found!")
    print("Install it with: pip install paramiko")
    sys.exit(1)


class MangaUploader:
    """<PERSON>les uploading manga to TrueNAS server via SFTP."""
    
    def __init__(self, host: str, username: str, password: str, remote_base: str, local_base: str = "manga"):
        """
        Initialize the uploader.
        
        Args:
            host: Server hostname or IP
            username: SFTP username
            password: SFTP password
            remote_base: Remote base directory (e.g., /mnt/tank/manga/content/mangas)
            local_base: Local manga directory (default: manga)
        """
        self.host = host
        self.username = username
        self.password = password
        self.remote_base = remote_base
        self.local_base = Path(local_base)
        self.sftp = None
        self.ssh = None
        
    def connect(self) -> bool:
        """
        Connect to the SFTP server.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            print(f"Connecting to {self.host}...")
            
            # Create SSH client
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # Connect
            self.ssh.connect(
                hostname=self.host,
                username=self.username,
                password=self.password,
                timeout=10
            )
            
            # Open SFTP session
            self.sftp = self.ssh.open_sftp()
            
            print(f"✓ Connected to {self.host}")
            return True
            
        except Exception as e:
            print(f"✗ Failed to connect: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from the SFTP server."""
        if self.sftp:
            self.sftp.close()
        if self.ssh:
            self.ssh.close()
        print("✓ Disconnected from server")
    
    def get_remote_folders(self) -> List[str]:
        """
        Get list of manga folders on remote server.

        Returns:
            List of folder names in remote base directory
        """
        try:
            if self.sftp is None:
                return []
            folders = []
            for item in self.sftp.listdir_attr(self.remote_base):
                if item.st_mode is not None and (item.st_mode & 0o040000):  # Check if directory
                    folders.append(item.filename)
            return folders
        except Exception as e:
            print(f"⚠ Warning: Could not list remote base directory: {e}")
            return []

    def find_matching_remote_folder(self, local_manga_name: str, remote_folders: List[str]) -> Optional[str]:
        """
        Find matching remote folder for local manga.
        Tries to match by comparing sanitized names.

        Args:
            local_manga_name: Local manga folder name
            remote_folders: List of remote folder names

        Returns:
            Matching remote folder name or None
        """
        # Normalize local name for comparison
        local_normalized = local_manga_name.lower().replace(' ', '').replace('-', '').replace('_', '')

        # Try exact match first
        if local_manga_name in remote_folders:
            return local_manga_name

        # Try normalized matching
        for remote_folder in remote_folders:
            remote_normalized = remote_folder.lower().replace(' ', '').replace('-', '').replace('_', '').replace("'", '').replace('"', '').replace('!', '').replace('?', '')
            if local_normalized == remote_normalized:
                return remote_folder

        # Try partial matching (local name is part of remote name)
        for remote_folder in remote_folders:
            if local_normalized in remote_folder.lower().replace(' ', '').replace('-', '').replace('_', '').replace("'", '').replace('"', '').replace('!', '').replace('?', ''):
                return remote_folder

        return None

    def get_remote_files(self, remote_dir: str) -> Set[str]:
        """
        Get list of files in remote directory.

        Args:
            remote_dir: Remote directory path

        Returns:
            Set of filenames in the directory
        """
        try:
            if self.sftp is None:
                return set()
            files = set(self.sftp.listdir(remote_dir))
            return files
        except FileNotFoundError:
            return set()
        except Exception as e:
            print(f"⚠ Warning: Could not list remote directory {remote_dir}: {e}")
            return set()
    
    def create_remote_dir(self, remote_dir: str) -> bool:
        """
        Create remote directory if it doesn't exist.
        
        Args:
            remote_dir: Remote directory path
            
        Returns:
            True if successful or already exists, False otherwise
        """
        try:
            self.sftp.stat(remote_dir)
            return True  # Directory exists
        except FileNotFoundError:
            try:
                self.sftp.mkdir(remote_dir)
                print(f"  ✓ Created remote directory: {remote_dir}")
                return True
            except Exception as e:
                print(f"  ✗ Failed to create directory {remote_dir}: {e}")
                return False
        except Exception as e:
            print(f"  ⚠ Warning: Could not check directory {remote_dir}: {e}")
            return False
    
    def upload_file(self, local_path: Path, remote_path: str) -> bool:
        """
        Upload a file to the server.
        
        Args:
            local_path: Local file path
            remote_path: Remote file path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get file size for progress
            file_size = local_path.stat().st_size
            file_size_mb = file_size / (1024 * 1024)
            
            print(f"  ↑ Uploading: {local_path.name} ({file_size_mb:.2f} MB)...", end='', flush=True)
            
            # Upload file
            self.sftp.put(str(local_path), remote_path)
            
            print(" ✓")
            return True
            
        except Exception as e:
            print(f" ✗")
            print(f"    Error: {e}")
            return False
    
    def sync_manga(self) -> dict:
        """
        Sync local manga folder to remote server.

        Returns:
            Dictionary with sync statistics
        """
        stats = {
            'manga_processed': 0,
            'manga_created': 0,
            'files_uploaded': 0,
            'files_skipped': 0,
            'files_failed': 0,
            'total_size_mb': 0.0
        }

        if not self.local_base.exists():
            print(f"✗ Local manga directory not found: {self.local_base}")
            return stats

        # Get list of manga folders
        manga_folders = [d for d in self.local_base.iterdir() if d.is_dir()]

        if not manga_folders:
            print("No manga folders found in local directory")
            return stats

        print(f"\nFound {len(manga_folders)} manga folder(s) to sync")
        print(f"Remote base: {self.remote_base}")

        # Get existing remote folders
        print("Fetching remote folder list...")
        remote_folders = self.get_remote_folders()
        if remote_folders:
            print(f"Found {len(remote_folders)} existing manga folder(s) on server\n")
        else:
            print("No existing manga folders on server\n")

        # Process each manga folder
        for manga_dir in manga_folders:
            manga_name = manga_dir.name
            stats['manga_processed'] += 1

            print(f"[{stats['manga_processed']}/{len(manga_folders)}] Processing: {manga_name}")

            # Try to find matching remote folder
            matching_remote = self.find_matching_remote_folder(manga_name, remote_folders)

            if matching_remote:
                print(f"  ✓ Found matching remote folder: '{matching_remote}'")
                remote_manga_dir = f"{self.remote_base}/{matching_remote}"
            else:
                print(f"  ℹ️  No matching remote folder, will create: '{manga_name}'")
                remote_manga_dir = f"{self.remote_base}/{manga_name}"

                # Create new directory
                if not self.create_remote_dir(remote_manga_dir):
                    print(f"  ✗ Skipping {manga_name} (could not create remote directory)")
                    continue

                stats['manga_created'] += 1
            
            # Get list of remote files
            remote_files = self.get_remote_files(remote_manga_dir)
            
            if remote_files:
                print(f"  ℹ️  Remote has {len(remote_files)} file(s)")
            else:
                print(f"  ℹ️  Remote directory is empty (new manga)")
            
            # Get local CBZ files
            local_files = list(manga_dir.glob('*.cbz'))
            
            if not local_files:
                print(f"  ⚠ No CBZ files found locally")
                continue
            
            print(f"  ℹ️  Local has {len(local_files)} file(s)")
            
            # Upload files that don't exist remotely
            uploaded = 0
            skipped = 0
            
            for local_file in sorted(local_files):
                filename = local_file.name
                
                if filename in remote_files:
                    skipped += 1
                    stats['files_skipped'] += 1
                else:
                    remote_file_path = f"{remote_manga_dir}/{filename}"
                    
                    if self.upload_file(local_file, remote_file_path):
                        uploaded += 1
                        stats['files_uploaded'] += 1
                        stats['total_size_mb'] += local_file.stat().st_size / (1024 * 1024)
                    else:
                        stats['files_failed'] += 1
            
            # Summary for this manga
            if uploaded > 0:
                print(f"  ✓ Uploaded {uploaded} new file(s)")
            if skipped > 0:
                print(f"  ⊘ Skipped {skipped} existing file(s)")
            
            print()
        
        return stats
    
    def run(self):
        """Main execution method."""
        print("=" * 80)
        print("Manga SFTP Uploader")
        print("=" * 80)
        
        # Connect to server
        if not self.connect():
            sys.exit(1)
        
        try:
            # Sync manga
            stats = self.sync_manga()
            
            # Print summary
            print("=" * 80)
            print("Upload Summary")
            print("=" * 80)
            print(f"Manga processed: {stats['manga_processed']}")
            print(f"New manga folders: {stats['manga_created']}")
            print(f"Files uploaded: {stats['files_uploaded']}")
            print(f"Files skipped: {stats['files_skipped']}")
            print(f"Files failed: {stats['files_failed']}")
            print(f"Total uploaded: {stats['total_size_mb']:.2f} MB")
            print("=" * 80)
            
        finally:
            # Always disconnect
            self.disconnect()


if __name__ == "__main__":
    # Server configuration
    HOST = "*************"
    USERNAME = "truenas_admin"
    PASSWORD = "gamer"
    REMOTE_BASE = "/mnt/tank/manga/content/mangas"
    LOCAL_BASE = "manga"
    
    # Create uploader and run
    uploader = MangaUploader(
        host=HOST,
        username=USERNAME,
        password=PASSWORD,
        remote_base=REMOTE_BASE,
        local_base=LOCAL_BASE
    )
    
    uploader.run()

