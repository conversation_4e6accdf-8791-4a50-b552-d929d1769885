#!/usr/bin/env python3
"""
MangaDex Downloader Script
Downloads manga from MangaDex using the mangadex-downloader library.
Reads URLs from mangas.txt and downloads chapters as CBZ files with incremental support.
"""

import re
import shutil
import subprocess
import sys
from pathlib import Path
from typing import List, Set, Tuple


class MangaDownloader:
    """Handles manga downloads from MangaDex."""

    def __init__(self, manga_root: str = "manga", temp_root: str = ".mangadex_temp", language: str = "en"):
        """
        Initialize the MangaDownloader.

        Args:
            manga_root: Root directory for manga downloads
            temp_root: Root directory for temporary downloads (kept for hash verification)
            language: Language code for downloads (default: en)
        """
        self.manga_root = Path(manga_root)
        self.temp_root = Path(temp_root)
        self.language = language
        self.manga_root.mkdir(exist_ok=True)
        self.temp_root.mkdir(exist_ok=True)

    def read_manga_urls(self, file_path: str = "mangas.txt") -> List[str]:
        """
        Read manga URLs from a file.

        Args:
            file_path: Path to the file containing manga URLs

        Returns:
            List of manga URLs
        """
        urls = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        urls.append(line)
            print(f"✓ Found {len(urls)} manga URL(s) in {file_path}")
            return urls
        except FileNotFoundError:
            print(f"✗ Error: {file_path} not found!")
            sys.exit(1)
        except Exception as e:
            print(f"✗ Error reading {file_path}: {e}")
            sys.exit(1)

    def extract_manga_id(self, url: str) -> str:
        """
        Extract manga ID from MangaDex URL.

        Args:
            url: MangaDex URL

        Returns:
            Manga ID
        """
        # Match patterns like: https://mangadex.org/title/UUID/manga-name
        match = re.search(r'/title/([a-f0-9-]+)', url)
        if match:
            return match.group(1)
        return url

    def get_existing_chapters(self, manga_dir: Path) -> Set[Tuple[str, str]]:
        """
        Get set of existing chapters in a manga directory.

        Args:
            manga_dir: Path to manga directory

        Returns:
            Set of tuples (volume, chapter) for existing chapters
        """
        existing = set()
        if not manga_dir.exists():
            return existing

        # Pattern: Manga Name v001 c002.cbz or Manga Name c002.cbz
        pattern = re.compile(r'v(\d+)\s+c(\d+)\.cbz|c(\d+)\.cbz', re.IGNORECASE)

        for file in manga_dir.glob('*.cbz'):
            match = pattern.search(file.name)
            if match:
                if match.group(1):  # Has volume
                    volume = match.group(1)
                    chapter = match.group(2)
                else:  # No volume
                    volume = "000"
                    chapter = match.group(3)
                existing.add((volume, chapter))

        return existing

    def check_mangadex_downloader(self) -> bool:
        """
        Check if mangadex-downloader is installed.

        Returns:
            True if installed, False otherwise
        """
        try:
            result = subprocess.run(
                ['mangadex-dl', '--version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                print(f"✓ mangadex-downloader is installed: {result.stdout.strip()}")
                return True
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass

        print("✗ mangadex-downloader is not installed!")
        print("\nTo install, run:")
        print("  pip install mangadex-downloader")
        print("  or")
        print("  pip install mangadex-downloader[optional]  # For full features")
        return False

    def get_manga_info(self, url: str) -> Tuple[str, str]:
        """
        Get manga title by attempting a dry-run download.

        Args:
            url: MangaDex URL

        Returns:
            Tuple of (manga_title, manga_id)
        """
        manga_id = self.extract_manga_id(url)

        # Try to extract title from URL first
        title_match = re.search(r'/title/[a-f0-9-]+/(.+?)(?:\?|$)', url)
        if title_match:
            title = title_match.group(1).replace('-', ' ').title()
            return title, manga_id

        return f"manga_{manga_id[:8]}", manga_id

    def download_manga(self, url: str) -> bool:
        """
        Download manga from MangaDex URL.

        Args:
            url: MangaDex URL

        Returns:
            True if successful, False otherwise
        """
        print(f"\n{'='*80}")
        print(f"Processing: {url}")
        print(f"{'='*80}")

        # Get manga info
        manga_title, manga_id = self.get_manga_info(url)
        print(f"Manga: {manga_title}")
        print(f"ID: {manga_id}")

        # Create final manga directory
        manga_dir = self.manga_root / self.sanitize_filename(manga_title)
        manga_dir.mkdir(exist_ok=True)
        print(f"Final Directory: {manga_dir}")

        # Get existing chapters
        existing_chapters = self.get_existing_chapters(manga_dir)
        if existing_chapters:
            print(f"✓ Found {len(existing_chapters)} existing chapter(s)")

        # Create manga-specific temp directory (persistent for hash verification)
        temp_manga_dir = self.temp_root / self.sanitize_filename(manga_title)
        temp_manga_dir.mkdir(exist_ok=True)
        print(f"Temp Directory: {temp_manga_dir}")

        # Build mangadex-dl command
        cmd = [
            'mangadex-dl',
            url,
            '--save-as', 'cbz',  # Save as CBZ format
            '--language', self.language,  # Set language
            '--path', str(temp_manga_dir),  # Persistent temp directory
            '--no-oneshot-chapter',  # Skip oneshot chapters
            '--sort-by', 'chapter',  # Sort by chapter
            '--order', 'oldest',  # Download oldest first
        ]

        print(f"\nExecuting download command...")
        print(f"Command: {' '.join(cmd)}")

        try:
            # Run the download command
            result = subprocess.run(
                cmd,
                text=True,
                capture_output=False,  # Show output in real-time
                timeout=3600  # 1 hour timeout
            )

            if result.returncode == 0:
                print(f"\n✓ Successfully downloaded manga: {manga_title}")

                # Process and copy files from temp to final directory
                self.process_and_move_chapters(temp_manga_dir, manga_dir, manga_title, existing_chapters)
                return True
            else:
                print(f"\n✗ Failed to download manga: {manga_title}")
                print(f"Exit code: {result.returncode}")
                return False

        except subprocess.TimeoutExpired:
            print(f"\n✗ Download timed out for: {manga_title}")
            return False
        except Exception as e:
            print(f"\n✗ Error downloading {manga_title}: {e}")
            return False

    def sanitize_filename(self, filename: str) -> str:
        """
        Sanitize filename by removing invalid characters.

        Args:
            filename: Original filename

        Returns:
            Sanitized filename
        """
        # Remove invalid characters for Windows/Linux
        invalid_chars = r'[<>:"/\\|?*]'
        sanitized = re.sub(invalid_chars, '', filename)
        # Replace multiple spaces with single space
        sanitized = re.sub(r'\s+', ' ', sanitized)
        return sanitized.strip()

    def process_and_move_chapters(self, temp_dir: Path, manga_dir: Path, manga_title: str, existing_chapters: Set[Tuple[str, str]]):
        """
        Process downloaded chapters from temp directory and copy to final directory.
        Keeps original files in temp for hash verification by mangadex-downloader.
        Format: Manga Name v00X c00X.cbz

        Args:
            temp_dir: Temporary download directory (persistent)
            manga_dir: Final manga directory
            manga_title: Manga title
            existing_chapters: Set of existing chapters (unused, kept for compatibility)
        """
        print(f"\nProcessing and copying chapters to organized folder...")

        # Pattern to match various chapter naming formats from mangadex-dl
        # Examples: "Vol. 1 Ch. 1.cbz", "Ch. 1.cbz", "Volume 1 Chapter 1.cbz"
        patterns = [
            re.compile(r'.*?[vV]ol[ume.]*\s*(\d+).*?[cC]h[apter.]*\s*(\d+(?:\.\d+)?).*?\.cbz', re.IGNORECASE),
            re.compile(r'.*?[cC]h[apter.]*\s*(\d+(?:\.\d+)?).*?\.cbz', re.IGNORECASE),
        ]

        copied_count = 0
        skipped_count = 0

        for file in temp_dir.glob('*.cbz'):
            original_name = file.name

            # Try to match with volume and chapter
            matched = False
            for pattern in patterns:
                match = pattern.match(original_name)
                if match:
                    if len(match.groups()) == 2:  # Has volume
                        volume = match.group(1).zfill(3)
                        chapter_raw = match.group(2)
                        chapter = chapter_raw.replace('.', '-')

                        # Pad chapter number properly
                        if '-' in chapter:
                            parts = chapter.split('-')
                            chapter = f"{parts[0].zfill(3)}-{parts[1]}"
                        else:
                            chapter = chapter.zfill(3)

                        new_name = f"{manga_title} v{volume} c{chapter}.cbz"
                    else:  # No volume
                        chapter_raw = match.group(1)
                        chapter = chapter_raw.replace('.', '-')

                        # Pad chapter number properly
                        if '-' in chapter:
                            parts = chapter.split('-')
                            chapter = f"{parts[0].zfill(3)}-{parts[1]}"
                        else:
                            chapter = chapter.zfill(3)

                        new_name = f"{manga_title} c{chapter}.cbz"

                    new_path = manga_dir / self.sanitize_filename(new_name)

                    # Check if chapter already exists in final directory
                    if new_path.exists():
                        skipped_count += 1
                    else:
                        try:
                            # Copy file to final destination (keep original in temp)
                            shutil.copy2(file, new_path)
                            print(f"  ✓ Copied: {original_name} -> {new_name}")
                            copied_count += 1
                        except Exception as e:
                            print(f"  ✗ Failed to copy {original_name}: {e}")

                    matched = True
                    break

            if not matched and not original_name.startswith('cover'):
                print(f"  ⚠ Skipped: {original_name} (couldn't parse format)")

        # Also copy cover image if exists
        for cover_file in temp_dir.glob('cover.*'):
            cover_dest = manga_dir / cover_file.name
            if not cover_dest.exists():
                try:
                    shutil.copy2(cover_file, cover_dest)
                    print(f"  ✓ Copied cover: {cover_file.name}")
                except Exception as e:
                    print(f"  ⚠ Failed to copy cover: {e}")

        if copied_count > 0:
            print(f"\n📊 Summary: {copied_count} new chapter(s) copied to organized folder")
        else:
            print(f"\n📊 Summary: No new chapters to copy (all up to date)")

        if skipped_count > 0:
            print(f"ℹ️  Note: {skipped_count} chapter(s) already exist in organized folder")

    def run(self):
        """Main execution method."""
        print("=" * 80)
        print("MangaDex Downloader")
        print("=" * 80)

        # Check if mangadex-downloader is installed
        if not self.check_mangadex_downloader():
            sys.exit(1)

        # Read manga URLs
        urls = self.read_manga_urls()

        if not urls:
            print("No URLs found in mangas.txt")
            sys.exit(1)

        # Download each manga
        success_count = 0
        failed_count = 0

        for i, url in enumerate(urls, 1):
            print(f"\n[{i}/{len(urls)}] Processing manga...")
            if self.download_manga(url):
                success_count += 1
            else:
                failed_count += 1

        # Summary
        print(f"\n{'='*80}")
        print("Download Summary")
        print(f"{'='*80}")
        print(f"Total: {len(urls)}")
        print(f"✓ Successful: {success_count}")
        print(f"✗ Failed: {failed_count}")
        print(f"{'='*80}")


if __name__ == "__main__":
    downloader = MangaDownloader(
        manga_root="manga",
        temp_root=".mangadex_temp",
        language="en"
    )
    downloader.run()
