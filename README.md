# MangaDex Downloader Script

A Python script that downloads manga from MangaDex using the `mangadex-downloader` library with support for incremental downloads and organized file structure.

## Features

### Download Features
- ✅ **Batch Downloads**: Read multiple manga URLs from `mangas.txt`
- ✅ **Organized Structure**: Creates a `manga` folder with subfolders for each manga
- ✅ **CBZ Format**: Downloads chapters as CBZ files (Comic Book Archive)
- ✅ **Standardized Naming**: Files named as `Manga Name v00X c00X.cbz`
- ✅ **Incremental Downloads**: Automatically skips already downloaded chapters
- ✅ **English Language**: Downloads in English by default
- ✅ **Error Handling**: Robust error handling for invalid URLs or network issues
- ✅ **Progress Tracking**: Shows download progress and summary

### Upload Features (NEW!)
- ✅ **SFTP Upload**: Upload manga to TrueNAS server via SFTP
- ✅ **Incremental Upload**: Only uploads new files, skips existing ones
- ✅ **Intelligent Folder Matching**: Automatically matches local folders to existing server folders
- ✅ **Automatic Folder Creation**: Creates manga folders on server if needed
- ✅ **Combined Workflow**: Download and upload in one command

## Prerequisites

- Python 3.10 or higher
- `mangadex-downloader` library

## Installation

1. **Install dependencies**:

```bash
pip install -r requirements.txt
```

Or install manually:

```bash
pip install mangadex-downloader  # For downloading
pip install paramiko             # For uploading to server
```

For full features (EPUB, CB7 support, etc.):

```bash
pip install mangadex-downloader[optional]
```

2. **Clone or download this repository**

## Usage

### Basic Usage (Download Only)

#### 1. Add Manga URLs

Edit the `mangas.txt` file and add MangaDex URLs (one per line):

```
https://mangadex.org/title/7d384ea2-1efd-4815-8c12-ff9663215060/boukensha-ni-narenakatta-ore-skill-oppai-kyousei-de-nayameru-anoko-wo-hitotasuke
https://mangadex.org/title/0a580438-bc72-4503-940b-12a5da881b56/kimi-wa-yotsuba-no-clover
```

You can also add comments using `#`:

```
# My favorite manga
https://mangadex.org/title/manga-id/manga-name

# Another manga to download
https://mangadex.org/title/another-id/another-name
```

#### 2. Download Manga

```bash
python main.py
```

### Advanced Usage (Download + Upload to Server)

#### 1. Configure Server (First Time Only)

Edit `upload_to_server.py` with your server details:

```python
HOST = "*************"
USERNAME = "truenas_admin"
PASSWORD = "gamer"
REMOTE_BASE = "/mnt/tank/manga/content/mangas"
```

#### 2. Download and Upload

**Option A: Download and upload in one command**
```bash
python download_and_upload.py
```

**Option B: Upload only (if already downloaded)**
```bash
python upload_to_server.py
```

See [UPLOAD_GUIDE.md](UPLOAD_GUIDE.md) for detailed upload instructions.

### 3. Output Structure

The script will create the following structure:

```
manga/                          # Organized manga with standardized names
├── Manga Name 1/
│   ├── Manga Name 1 v001 c001.cbz
│   ├── Manga Name 1 v001 c002.cbz
│   └── Manga Name 1 v002 c003.cbz
└── Manga Name 2/
    ├── Manga Name 2 c001.cbz
    └── Manga Name 2 c002.cbz

.mangadex_temp/                 # Original downloads (kept for hash verification)
├── Manga Name 1/
│   ├── Vol. 1 Ch. 1.cbz
│   ├── Vol. 1 Ch. 2.cbz
│   └── Vol. 2 Ch. 3.cbz
└── Manga Name 2/
    ├── Ch. 1.cbz
    └── Ch. 2.cbz
```

**Note**: The `.mangadex_temp/` folder contains the original downloads with their original names. This allows `mangadex-downloader` to verify files and avoid re-downloading. The `manga/` folder contains organized copies with standardized names.

## Configuration

You can modify the script behavior by editing the `main.py` file:

### Change Download Directory

```python
# Default: "manga"
downloader = MangaDownloader(manga_root="my_manga_folder", language="en")
```

### Change Language

```python
# Default: "en" (English)
# Available languages: en, ja, es, fr, de, etc.
downloader = MangaDownloader(manga_root="manga", language="ja")
```

To see all available languages, run:

```bash
mangadex-dl --list-languages
```

## How It Works

1. **Read URLs**: The script reads manga URLs from `mangas.txt`
2. **Check Installation**: Verifies that `mangadex-downloader` is installed
3. **Create Directories**: Creates both `manga` (organized) and `.mangadex_temp` (original) folders
4. **Check Existing Files**: Scans for already downloaded chapters in the organized folder
5. **Download**: Downloads chapters to `.mangadex_temp/[Manga Name]/` with original names
6. **Copy & Organize**: Copies only new chapters to `manga/[Manga Name]/` with standardized names
7. **Keep Originals**: Keeps original files in `.mangadex_temp/` for hash verification

### Why Keep Two Folders?

The script maintains two separate folders to get the best of both worlds:

**`.mangadex_temp/` folder (Original Downloads)**
- ✅ Keeps files with original names from MangaDex
- ✅ Allows `mangadex-downloader` to verify file hashes
- ✅ Prevents re-downloading of existing chapters
- ✅ No hash verification warnings

**`manga/` folder (Organized Collection)**
- ✅ Standardized naming: `Manga Name v00X c00X.cbz`
- ✅ Easy to browse and read
- ✅ Compatible with manga readers
- ✅ Clean organization by manga title

This approach ensures that `mangadex-downloader` can properly track downloads while you get a beautifully organized manga collection!

## Incremental Downloads

The script automatically detects existing chapters and skips them. This means you can:

- ✅ Run the script multiple times to download new chapters
- ✅ `mangadex-downloader` verifies existing files in `.mangadex_temp/`
- ✅ Only new chapters are downloaded and copied to `manga/`
- ✅ No re-downloading, no hash warnings, no wasted bandwidth

**Example**: If you have chapters 1-10 and run the script again, it will:
1. Check `.mangadex_temp/` and find chapters 1-10 already exist
2. Download only new chapters (11, 12, etc.) to `.mangadex_temp/`
3. Copy only the new chapters to `manga/` with standardized names
4. Skip chapters 1-10 entirely (no download, no copy)

## File Naming Convention

- **With Volume**: `Manga Name v001 c002.cbz`
  - `v001` = Volume 1 (zero-padded to 3 digits)
  - `c002` = Chapter 2 (zero-padded to 3 digits)

- **Without Volume**: `Manga Name c002.cbz`
  - `c002` = Chapter 2 (zero-padded to 3 digits)

## Troubleshooting

### mangadex-downloader not found

If you get an error that `mangadex-dl` is not found:

```bash
pip install mangadex-downloader
```

Or check if it's installed:

```bash
mangadex-dl --version
```

### Invalid URL

Make sure your URLs are in the correct format:

```
https://mangadex.org/title/[UUID]/[manga-name]
```

### Network Errors

If you encounter network errors:

- Check your internet connection
- Try again later (MangaDex might be under maintenance)
- The script respects MangaDex API rate limits

### Permission Errors

If you get permission errors when creating directories:

- Make sure you have write permissions in the current directory
- Try running the script from a different location

## Advanced Usage

### Custom Filename Format

The script uses a standardized naming convention, but you can modify the `rename_chapters()` method in `main.py` to customize the format.

### Download Specific Chapters

To download specific chapters, you can use the `mangadex-dl` CLI directly:

```bash
mangadex-dl "URL" --start-chapter 10 --end-chapter 20 --save-as cbz --language en
```

### Authentication

If you want to download from your library or access age-restricted content:

```bash
mangadex-dl "URL" --login --save-as cbz --language en
```

## Credits

- [mangadex-downloader](https://github.com/mansuf/mangadex-downloader) by mansuf
- [MangaDex](https://mangadex.org/) - The manga reading platform

## License

This script is provided as-is for personal use. Please respect MangaDex's terms of service and API rate limits.

## Disclaimer

This tool is for personal use only. Please support manga authors and publishers by purchasing official releases when available.

