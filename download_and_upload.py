#!/usr/bin/env python3
"""
Download manga from MangaDex and upload to TrueNAS server.
This script combines the download and upload functionality.
"""

import sys
import subprocess
from pathlib import Path


def run_download():
    """Run the manga download script."""
    print("=" * 80)
    print("STEP 1: Downloading Manga from MangaDex")
    print("=" * 80)
    print()
    
    try:
        result = subprocess.run(
            [sys.executable, "main.py"],
            check=False
        )
        
        if result.returncode == 0:
            print("\n✓ Download completed successfully")
            return True
        else:
            print(f"\n✗ Download failed with exit code {result.returncode}")
            return False
            
    except Exception as e:
        print(f"\n✗ Error running download script: {e}")
        return False


def run_upload():
    """Run the manga upload script."""
    print("\n" + "=" * 80)
    print("STEP 2: Uploading Manga to TrueNAS Server")
    print("=" * 80)
    print()
    
    try:
        result = subprocess.run(
            [sys.executable, "upload_to_server.py"],
            check=False
        )
        
        if result.returncode == 0:
            print("\n✓ Upload completed successfully")
            return True
        else:
            print(f"\n✗ Upload failed with exit code {result.returncode}")
            return False
            
    except Exception as e:
        print(f"\n✗ Error running upload script: {e}")
        return False


def main():
    """Main execution."""
    print("=" * 80)
    print("MangaDex Downloader & TrueNAS Uploader")
    print("=" * 80)
    print()
    
    # Check if manga folder exists
    manga_dir = Path("manga")
    if not manga_dir.exists():
        print("Creating manga directory...")
        manga_dir.mkdir(exist_ok=True)
    
    # Step 1: Download
    download_success = run_download()
    
    if not download_success:
        print("\n⚠ Download failed, skipping upload")
        sys.exit(1)
    
    # Step 2: Upload
    upload_success = run_upload()
    
    # Final summary
    print("\n" + "=" * 80)
    print("FINAL SUMMARY")
    print("=" * 80)
    
    if download_success and upload_success:
        print("✓ Download: SUCCESS")
        print("✓ Upload: SUCCESS")
        print("\n🎉 All operations completed successfully!")
    elif download_success:
        print("✓ Download: SUCCESS")
        print("✗ Upload: FAILED")
        print("\n⚠ Download succeeded but upload failed")
    else:
        print("✗ Download: FAILED")
        print("- Upload: SKIPPED")
        print("\n✗ Operations failed")
    
    print("=" * 80)


if __name__ == "__main__":
    main()

